version: '3'

vars:
  APP_NAME: assistant-go
  BUILD_DIR: ./build
  VERSION: 1.0.0

tasks:
  default:
    desc: 顯示可用的任務
    cmds:
      - task --list

  build:
    desc: 🔨 建置應用程式
    cmds:
      - echo "建置 {{.APP_NAME}}..."
      - go build -ldflags "-X main.version={{.VERSION}}" -o {{.BUILD_DIR}}/{{.APP_NAME}} .
      - echo "✅ 建置完成！"

  run:
    desc: 🚀 執行應用程式
    deps: [build]
    cmds:
      - echo "啟動 {{.APP_NAME}}..."
      - ./{{.BUILD_DIR}}/{{.APP_NAME}}

  test:
    desc: 🧪 執行所有測試
    cmds:
      - echo "執行測試..."
      - go test -v ./...
      - echo "✅ 測試完成！"

  test-cover:
    desc: 📊 執行測試並生成覆蓋率報告
    cmds:
      - echo "執行測試覆蓋率分析..."
      - go test -v -cover -coverprofile=coverage.out ./...
      - go tool cover -html=coverage.out -o coverage.html
      - echo "✅ 覆蓋率報告已生成：coverage.html"

  clean:
    desc: 🧹 清理建置檔案
    cmds:
      - echo "清理建置檔案..."
      - rm -rf {{.BUILD_DIR}}
      - rm -f coverage.out coverage.html
      - go clean -cache -modcache -testcache
      - echo "✅ 清理完成！"

  deps:
    desc: 📦 安裝和更新依賴
    cmds:
      - echo "更新依賴..."
      - go mod tidy
      - go mod download
      - echo "✅ 依賴更新完成！"

  fmt:
    desc: ✨ 格式化程式碼
    cmds:
      - echo "格式化程式碼..."
      - go fmt ./...
      - echo "✅ 程式碼格式化完成！"

  lint:
    desc: 🔍 程式碼檢查
    cmds:
      - echo "執行程式碼檢查..."
      - go vet ./...
      - echo "✅ 程式碼檢查完成！"

  dev:
    desc: 🔄 開發模式（自動重新載入）
    cmds:
      - echo "啟動開發模式..."
      - echo "注意：需要安裝 air (go install github.com/cosmtrek/air@latest)"
      - air

  docker-build:
    desc: 🐳 建置 Docker 映像
    cmds:
      - echo "建置 Docker 映像..."
      - docker build -t {{.APP_NAME}}:{{.VERSION}} .
      - docker tag {{.APP_NAME}}:{{.VERSION}} {{.APP_NAME}}:latest
      - echo "✅ Docker 映像建置完成！"

  docker-run:
    desc: 🐳 執行 Docker 容器
    deps: [docker-build]
    cmds:
      - echo "啟動 Docker 容器..."
      - docker run -p 8080:8080 --name {{.APP_NAME}} {{.APP_NAME}}:latest

  docker-stop:
    desc: 🛑 停止 Docker 容器
    cmds:
      - echo "停止 Docker 容器..."
      - docker stop {{.APP_NAME}} || true
      - docker rm {{.APP_NAME}} || true

  release:
    desc: 🚀 建置發布版本
    deps: [clean, test, lint]
    cmds:
      - echo "建置發布版本..."
      - mkdir -p {{.BUILD_DIR}}/release
      - GOOS=darwin GOARCH=amd64 go build -ldflags "-X main.version={{.VERSION}}" -o {{.BUILD_DIR}}/release/{{.APP_NAME}}-darwin-amd64 .
      - GOOS=linux GOARCH=amd64 go build -ldflags "-X main.version={{.VERSION}}" -o {{.BUILD_DIR}}/release/{{.APP_NAME}}-linux-amd64 .
      - GOOS=windows GOARCH=amd64 go build -ldflags "-X main.version={{.VERSION}}" -o {{.BUILD_DIR}}/release/{{.APP_NAME}}-windows-amd64.exe .
      - echo "✅ 發布版本建置完成！"

  install:
    desc: 📥 安裝到系統
    deps: [build]
    cmds:
      - echo "安裝 {{.APP_NAME}} 到系統..."
      - go install .
      - echo "✅ 安裝完成！"

  benchmark:
    desc: ⚡ 執行效能測試
    cmds:
      - echo "執行效能測試..."
      - go test -bench=. -benchmem ./...
      - echo "✅ 效能測試完成！"

  security:
    desc: 🔒 安全性檢查
    cmds:
      - echo "執行安全性檢查..."
      - echo "注意：需要安裝 gosec (go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest)"
      - gosec ./... || echo "gosec 未安裝，跳過安全性檢查"

  update:
    desc: 🔄 更新專案依賴
    cmds:
      - echo "更新專案依賴..."
      - go get -u ./...
      - go mod tidy
      - echo "✅ 依賴更新完成！"
