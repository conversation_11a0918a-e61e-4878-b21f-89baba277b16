# <PERSON>opa's Development Assistant Make<PERSON><PERSON>
APP_NAME := assistant-go
BUILD_DIR := ./build
VERSION := 1.0.0
LDFLAGS := -ldflags "-X main.version=$(VERSION)"

# 預設目標
.PHONY: help
help: ## 📋 顯示可用的命令
	@echo "🚀 <PERSON><PERSON><PERSON>'s Development Assistant"
	@echo "================================"
	@awk 'BEGIN {FS = ":.*##"} /^[a-zA-Z_-]+:.*##/ { printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2 }' $(MAKEFILE_LIST)

.PHONY: build
build: ## 🔨 建置應用程式
	@echo "🔨 建置 $(APP_NAME)..."
	@mkdir -p $(BUILD_DIR)
	@go build $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME) .
	@echo "✅ 建置完成！"

.PHONY: run
run: build ## 🚀 執行應用程式
	@echo "🚀 啟動 $(APP_NAME)..."
	@./$(BUILD_DIR)/$(APP_NAME)

.PHONY: test
test: ## 🧪 執行所有測試
	@echo "🧪 執行測試..."
	@go test -v ./...
	@echo "✅ 測試完成！"

.PHONY: test-cover
test-cover: ## 📊 執行測試並生成覆蓋率報告
	@echo "📊 執行測試覆蓋率分析..."
	@go test -v -cover -coverprofile=coverage.out ./...
	@go tool cover -html=coverage.out -o coverage.html
	@echo "✅ 覆蓋率報告已生成：coverage.html"

.PHONY: clean
clean: ## 🧹 清理建置檔案
	@echo "🧹 清理建置檔案..."
	@rm -rf $(BUILD_DIR)
	@rm -f coverage.out coverage.html
	@go clean -cache -modcache -testcache
	@echo "✅ 清理完成！"

.PHONY: deps
deps: ## 📦 安裝和更新依賴
	@echo "📦 更新依賴..."
	@go mod tidy
	@go mod download
	@echo "✅ 依賴更新完成！"

.PHONY: fmt
fmt: ## ✨ 格式化程式碼
	@echo "✨ 格式化程式碼..."
	@go fmt ./...
	@echo "✅ 程式碼格式化完成！"

.PHONY: lint
lint: ## 🔍 程式碼檢查
	@echo "🔍 執行程式碼檢查..."
	@go vet ./...
	@echo "✅ 程式碼檢查完成！"

.PHONY: dev
dev: ## 🔄 開發模式（需要 air）
	@echo "🔄 啟動開發模式..."
	@echo "注意：需要安裝 air (go install github.com/cosmtrek/air@latest)"
	@air

.PHONY: docker-build
docker-build: ## 🐳 建置 Docker 映像
	@echo "🐳 建置 Docker 映像..."
	@docker build -t $(APP_NAME):$(VERSION) .
	@docker tag $(APP_NAME):$(VERSION) $(APP_NAME):latest
	@echo "✅ Docker 映像建置完成！"

.PHONY: docker-run
docker-run: docker-build ## 🐳 執行 Docker 容器
	@echo "🐳 啟動 Docker 容器..."
	@docker run -p 8080:8080 --name $(APP_NAME) $(APP_NAME):latest

.PHONY: docker-stop
docker-stop: ## 🛑 停止 Docker 容器
	@echo "🛑 停止 Docker 容器..."
	@docker stop $(APP_NAME) || true
	@docker rm $(APP_NAME) || true

.PHONY: release
release: clean test lint ## 🚀 建置發布版本
	@echo "🚀 建置發布版本..."
	@mkdir -p $(BUILD_DIR)/release
	@GOOS=darwin GOARCH=amd64 go build $(LDFLAGS) -o $(BUILD_DIR)/release/$(APP_NAME)-darwin-amd64 .
	@GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o $(BUILD_DIR)/release/$(APP_NAME)-linux-amd64 .
	@GOOS=windows GOARCH=amd64 go build $(LDFLAGS) -o $(BUILD_DIR)/release/$(APP_NAME)-windows-amd64.exe .
	@echo "✅ 發布版本建置完成！"

.PHONY: install
install: build ## 📥 安裝到系統
	@echo "📥 安裝 $(APP_NAME) 到系統..."
	@go install .
	@echo "✅ 安裝完成！"

.PHONY: benchmark
benchmark: ## ⚡ 執行效能測試
	@echo "⚡ 執行效能測試..."
	@go test -bench=. -benchmem ./...
	@echo "✅ 效能測試完成！"

.PHONY: security
security: ## 🔒 安全性檢查
	@echo "🔒 執行安全性檢查..."
	@echo "注意：需要安裝 gosec (go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest)"
	@gosec ./... || echo "gosec 未安裝，跳過安全性檢查"

.PHONY: update
update: ## 🔄 更新專案依賴
	@echo "🔄 更新專案依賴..."
	@go get -u ./...
	@go mod tidy
	@echo "✅ 依賴更新完成！"

.PHONY: all
all: clean deps fmt lint test build ## 🎯 執行完整的建置流程
	@echo "🎯 完整建置流程完成！"
